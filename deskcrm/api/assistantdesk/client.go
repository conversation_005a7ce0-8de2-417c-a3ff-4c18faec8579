package assistantdesk

import (
	"deskcrm/api"
	"deskcrm/conf"
	json2 "deskcrm/libs/json"
	"deskcrm/libs/utils"
	"encoding/json"
	"errors"
	"fmt"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/mitchellh/mapstructure"
	"github.com/spf13/cast"
)

type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.AssistantDesk,
	}
	return c
}

const (
	GetCourseArkInfoAPI  = "/assistantdesk/api/deskcrm/getcoursearkinfo"
	GetCourseServicesAPI = "/assistantdesk/api/fwyytool/getcourseservices"

	GetExportStudentDataAPI = "/assistantdesk/api/filter/exportstudentlist"

	GetStudentListForDiffDataAPI       = "/assistantdesk/api/filter/studentlistfordiff"
	GetWxStudentListForDiffDataAPI     = "/assistantdesk/api/filter/wxstudentlistfordiff"
	GetCommonStudentListForDiffDataAPI = "/assistantdesk/api/filter/commonstudentlistfordiff"
	GetCustomTagListAPI                = "/assistantdesk/api/customtag/getcustomtaglist"
	GetUidsByTagAPI                    = "/assistantdesk/api/kptag/getuidsbytag"
	StudentFormatAPI                   = "/assistantdesk/api/ark/studentformat"

	GetKeyApi            = "/assistantdesk/api/keyconst/getkey"
	GetExamBindStatusAPI = "/assistantdesk/api/exam/getexambindstatus"
	CollectionAPI        = "/assistantdesk/api/task/collection"

	// PHP PerformanceV1 接口
	PerformanceV1API = "/assistantdesk/deskv1/student/performancev1"
)

func (c *Client) GetWxStudentListData(ctx *gin.Context, params GetStudentListDataParams) (studentData GetWxStudentListDataResp, err error) {
	resp := GetWxStudentListDataResp{}
	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, GetWxStudentListForDiffDataAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		zlog.Warnf(ctx, "GetWxStudentListData DecodeResponse err:%v,data:%v", err, string(res.Response))
		return
	}

	return resp, nil
}

func (c *Client) GetCommonStudentListData(ctx *gin.Context, params GetStudentListDataParams) (studentData GetCommonStudentListDataResp, err error) {
	resp := GetCommonStudentListDataResp{}
	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, GetCommonStudentListForDiffDataAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		zlog.Warnf(ctx, "GetCommonStudentListData DecodeResponse err:%v,data:%v", err, string(res.Response))
		return
	}

	return resp, nil
}

func (c *Client) GetStudentListData(ctx *gin.Context, params GetStudentListDataParams) (studentData GetStudentListDataResp, err error) {
	resp := GetStudentListDataResp{}
	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, GetStudentListForDiffDataAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		zlog.Warnf(ctx, "GetStudentListData DecodeResponse err:%v,data:%v", err, string(res.Response))
		return
	}

	return resp, nil
}

func (c *Client) GetExportStudentData(ctx *gin.Context, params GetExportStudentDataParams) (studentData [][]string, err error) {
	resp := GetExportStudentDataResp{}
	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, GetExportStudentDataAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return resp.Data, nil
}

func (c *Client) GetCourseServices(ctx *gin.Context, params GetCourseServicesParams) (serviceList []*CourseServiceList, err error) {
	resp := GetCourseServicesResp{}
	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, GetCourseServicesAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return resp.ServiceList, nil
}

func (c *Client) GetCourseArkInfo(ctx *gin.Context, params GetCourseArkInfoParams) (serviceList []*GetCourseArkInfoServiceList, err error) {
	resp := GetCourseArkInfoResp{}
	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, GetCourseArkInfoAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return resp.ServiceList, nil
}

func (c *Client) GetCustomTagList(ctx *gin.Context, courseID int64, tagType int, studentUids []int64) (resp []*CustomTagListDetail, err error) {
	resp = make([]*CustomTagListDetail, 0)
	if len(studentUids) == 0 {
		return
	}

	studentUidsJsonString, _ := json2.MarshalToString(studentUids)

	params := map[string]interface{}{
		"courseId":    courseID,
		"tagType":     tagType,
		"studentUids": studentUidsJsonString,
	}
	opt := base.HttpRequestOptions{
		RequestBody: params,
	}
	res, err := conf.API.AssistantDesk.HttpPost(ctx, GetCustomTagListAPI, opt)
	if err != nil {
		zlog.Warnf(ctx, "ral request failed, path:%s, err:%s", GetCustomTagListAPI, err)
		return
	}

	// 处理httpcode
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	// 解析返回值
	if _, err := api.DecodeResponse(ctx, res, &resp); err != nil {
		return resp, errors.New(fmt.Sprintf("DecodeResponse res=%+v", string(res.Response)))
	}
	return
}

func (c *Client) GetUidsByTag(ctx *gin.Context, assistantUid int64, wxTagName interface{}) (studentUids []int64, err error) {
	resp := GetUidsByTagResp{}
	studentUids = make([]int64, 0)

	params := map[string]interface{}{
		"assistantUid": assistantUid,
		"wxTagName":    wxTagName,
	}
	opt := base.HttpRequestOptions{
		RequestBody: params,
	}
	res, err := conf.API.AssistantDesk.HttpPost(ctx, GetUidsByTagAPI, opt)
	if err != nil {
		zlog.Warnf(ctx, "ral request failed, path:%s, err:%s", GetUidsByTagAPI, err)
		return
	}

	// 处理httpcode
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	// 解析返回值
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return studentUids, errors.New(fmt.Sprintf("DecodeResponse res=%+v", string(res.Response)))
	}
	studentUids = resp.StudentUids
	return studentUids, nil
}

type KeyConst map[string]interface{}

func (kc *KeyConst) ToSubjectIdNameMap() (subjectIdNameMap map[int64]string) {
	subjectIdNameMap = make(map[int64]string)
	if len(*kc) == 0 {
		return
	}

	for k, v := range *kc {
		sk := cast.ToInt64(k)
		sv := cast.ToString(v)
		if sk == 0 {
			continue
		}
		subjectIdNameMap[sk] = sv
	}
	return
}

func (kc *KeyConst) ToGradeStageMap() (gradeStageMap map[int64][]int64) {
	gradeStageMap = make(map[int64][]int64)
	gradeStageMapTemp := make(map[string][]int64)
	if len(*kc) == 0 {
		return
	}

	for _, v := range *kc {
		err := mapstructure.Decode(v, &gradeStageMapTemp)
		if err != nil {
			return nil
		}
	}

	for stage, grade := range gradeStageMapTemp {
		gradeStageMap[cast.ToInt64(stage)] = grade
	}

	return
}

func (c *Client) GetKeyConst(ctx *gin.Context, params GetKeyConstParams) (keyList *KeyConst, err error) {
	resp := map[string]KeyConst{}
	req := map[string]interface{}{
		"key": params.Key,
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, GetKeyApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, mp := range resp {
		// 取第一个（现在接口只支持单个查询）
		keyList = &mp
		return
	}
	return
}

type ExamBindRes struct {
	IsBind bool `json:"isBind"`
}

func (c *Client) GetExamBindStatus(ctx *gin.Context, params GetExamBindStatusParams) (isBind ExamBindRes, err error) {
	resp := ExamBindRes{}
	req := map[string]interface{}{
		"lessonId": params.LessonId,
		"bindType": params.BindType,
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, GetExamBindStatusAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	zlog.Infof(ctx, "request remote api GetExamBindStatus:%v", string(res.Response))

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return resp, nil
}

func (c *Client) Collection(ctx *gin.Context, params CollectionParam) (resp CollectionResp, err error) {
	resp = CollectionResp{}
	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, CollectionAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return resp, nil
}

// PerformanceV1Request PHP接口请求参数
type PerformanceV1Request struct {
	StudentUid   int64  `json:"studentUid"`
	PersonUid    int64  `json:"personUid"`
	CourseId     int64  `json:"courseId"`
	LeadsId      int64  `json:"leadsId"`
	Tab          string `json:"tab"`
	AssistantUid int64  `json:"assistantUid"`
	IsExport     int    `json:"isExport"`
}

// PerformanceV1Response PHP接口响应结构
type PerformanceV1Response struct {
	SchemaId    *string                  `json:"schemaId,omitempty"`
	TableData   []map[string]interface{} `json:"tableData"`
	TableHeader []map[string]interface{} `json:"tableHeader"`
}

// GetPerformanceV1 调用PHP版本的PerformanceV1接口
func (c *Client) GetPerformanceV1(ctx *gin.Context, req PerformanceV1Request) (*PerformanceV1Response, error) {
	// 构建请求参数
	requestData := map[string]interface{}{
		"studentUid":   req.StudentUid,
		"personUid":    req.PersonUid,
		"courseId":     req.CourseId,
		"leadsId":      req.LeadsId,
		"tab":          req.Tab,
		"assistantUid": req.AssistantUid,
		"isExport":     req.IsExport,
	}

	// 设置请求选项
	opts := base.HttpRequestOptions{
		RequestBody: requestData,
		Encode:      base.EncodeForm,
	}
	utils.DecorateHttpOptions(ctx, &opts)

	resp, err := c.cli.HttpPost(ctx, PerformanceV1API, opts)
	if err != nil {
		zlog.Errorf(ctx, "调用PHP接口失败: %v", err)
		return nil, fmt.Errorf("调用PHP接口失败: %w", err)
	}

	// 检查HTTP状态码
	if resp.HttpCode != 200 {
		zlog.Errorf(ctx, "PHP接口返回错误状态码: %d", resp.HttpCode)
		return nil, fmt.Errorf("PHP接口返回错误状态码: %d", resp.HttpCode)
	}

	var result *PerformanceV1Response
	if _, err := api.DecodeResponse(ctx, resp, &result); err != nil {
		zlog.Errorf(ctx, "解析PHP接口响应失败: %v", err)
		return nil, fmt.Errorf("解析PHP接口响应失败: %w", err)
	}

	return result, nil
}
