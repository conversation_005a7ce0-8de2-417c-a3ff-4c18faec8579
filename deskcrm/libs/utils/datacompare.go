package utils

import (
	"fmt"
	"reflect"
	"slices"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// NewComparisonResult 新的对比结果结构，按照要求的输出格式
type NewComparisonResult struct {
	TableData   []TableDataDiff   `json:"tableData"`
	TableHeader []TableHeaderDiff `json:"tableHeader"`
}

// TableDataDiff tableData的差异结构
type TableDataDiff struct {
	LessonId int64       `json:"lessonId"`
	Diff     []FieldDiff `json:"diff"`
}

// TableHeaderDiff tableHeader的差异结构
type TableHeaderDiff struct {
	Prop string      `json:"prop"`
	Diff []FieldDiff `json:"diff"`
}

// FieldDiff 字段差异详情
type FieldDiff struct {
	Field string      `json:"field"`
	PHP   interface{} `json:"php"`
	Go    interface{} `json:"go"`
}

// 对于tableData，以lessonId为主键进行匹配，然后对比其余的key
// 对于tableHeader，以prop为主键进行匹配，然后对比其余的key
// 以PHP为基准对比Go版本的数据，只考虑PHP中出现的字段
func ComparePerformanceV1Data(ctx *gin.Context, phpData, goData map[string][]map[string]interface{}) *NewComparisonResult {
	result := &NewComparisonResult{
		TableData:   []TableDataDiff{},
		TableHeader: []TableHeaderDiff{},
	}

	zlog.Infof(ctx, "开始新格式数据对比，以PHP版本为基准")

	// 对比 tableData
	if phpTableData, exists := phpData["tableData"]; exists {
		goTableData := goData["tableData"]
		tableDataDiffs := compareTableData(ctx, phpTableData, goTableData)
		result.TableData = tableDataDiffs
	}

	// 对比 tableHeader
	if phpTableHeader, exists := phpData["tableHeader"]; exists {
		goTableHeader := goData["tableHeader"]
		tableHeaderDiffs := compareTableHeaderNew(ctx, phpTableHeader, goTableHeader)
		result.TableHeader = tableHeaderDiffs
	}

	zlog.Infof(ctx, "新格式数据对比完成: tableData差异=%d, tableHeader差异=%d",
		len(result.TableData), len(result.TableHeader))

	return result
}

// compareTableData 对比tableData数组，以lessonId为主键匹配
func compareTableData(ctx *gin.Context, phpData, goData []map[string]interface{}) []TableDataDiff {
	var diffs []TableDataDiff

	// 创建Go数据的lessonId索引
	goIndexMap := make(map[int64]map[string]interface{})
	for _, goItem := range goData {
		if lessonId, exists := goItem["lessonId"]; exists {
			goIndexMap[cast.ToInt64(lessonId)] = goItem
		}
	}

	// 遍历PHP数据，以lessonId为主键进行匹配
	for _, phpItem := range phpData {
		lessonId, lessonIdExists := phpItem["lessonId"]
		if !lessonIdExists {
			continue
		}

		diff := TableDataDiff{
			LessonId: cast.ToInt64(lessonId),
			Diff:     []FieldDiff{},
		}

		// 查找对应的Go记录
		goMap, goExists := goIndexMap[cast.ToInt64(lessonId)]
		if !goExists {
			// Go中没有对应的lessonId记录
			for field, phpValue := range phpItem {
				diff.Diff = append(diff.Diff, FieldDiff{
					Field: field,
					PHP:   phpValue,
					Go:    nil,
				})
			}
		} else {
			// 对比除lessonId外的其他字段
			for field, phpValue := range phpItem {
				if !slices.Contains([]string{"lessonId", "preciseExercisesStatus"}, field) {
					goValue, goFieldExists := goMap[field]
					if !goFieldExists {
						// Go中缺失该字段
						diff.Diff = append(diff.Diff, FieldDiff{
							Field: field,
							PHP:   phpValue,
							Go:    nil,
						})
					} else if !valuesEqual(phpValue, goValue) {
						// 字段值不同
						diff.Diff = append(diff.Diff, FieldDiff{
							Field: field,
							PHP:   phpValue,
							Go:    goValue,
						})
					}
				}
			}
		}

		// 只有存在差异时才添加到结果中
		if len(diff.Diff) > 0 {
			diffs = append(diffs, diff)
		}
	}

	return diffs
}

// compareTableHeaderNew 对比tableHeader数组，以prop为主键匹配
func compareTableHeaderNew(ctx *gin.Context, phpData, goData []map[string]interface{}) []TableHeaderDiff {
	var diffs []TableHeaderDiff

	// 创建Go数据的prop索引
	goIndexMap := make(map[string]map[string]interface{})
	for _, goItem := range goData {
		if prop, exists := goItem["prop"]; exists {
			if propStr, ok := prop.(string); ok {
				goIndexMap[propStr] = goItem
			}
		}
	}

	// 遍历PHP数据，以prop为主键进行匹配
	for _, phpItem := range phpData {
		prop, propExists := phpItem["prop"]
		if !propExists {
			continue
		}

		propStr, propOk := prop.(string)
		if !propOk {
			continue
		}

		diff := TableHeaderDiff{
			Prop: propStr,
			Diff: []FieldDiff{},
		}

		// 查找对应的Go记录
		goMap, goExists := goIndexMap[propStr]
		if !goExists {
			// Go中没有对应的prop记录
			for field, phpValue := range phpItem {
				diff.Diff = append(diff.Diff, FieldDiff{
					Field: field,
					PHP:   phpValue,
					Go:    nil,
				})
			}
		} else {
			// 对比除prop外的其他字段
			for field, phpValue := range phpItem {
				if !slices.Contains([]string{"prop", "remark", "width", "hover"}, field) {
					goValue, goFieldExists := goMap[field]
					if !goFieldExists {
						// Go中缺失该字段
						diff.Diff = append(diff.Diff, FieldDiff{
							Field: field,
							PHP:   phpValue,
							Go:    nil,
						})
					} else if !valuesEqual(phpValue, goValue) {
						// 字段值不同
						diff.Diff = append(diff.Diff, FieldDiff{
							Field: field,
							PHP:   phpValue,
							Go:    goValue,
						})
					}
				}
			}
		}

		// 只有存在差异时才添加到结果中
		if len(diff.Diff) > 0 {
			diffs = append(diffs, diff)
		}
	}

	return diffs
}

// valuesEqual 比较两个值是否相等，不区分类型，只看值
func valuesEqual(a, b interface{}) bool {
	// 如果都是nil，则相等
	if a == nil && b == nil {
		return true
	}

	// 如果其中一个是nil，则不相等
	if a == nil || b == nil {
		return false
	}

	// 获取值的反射类型
	aVal := reflect.ValueOf(a)
	bVal := reflect.ValueOf(b)

	// 处理数值类型（int 和 float）
	if isNumeric(aVal) && isNumeric(bVal) {
		// 将值转换为 float64 进行比较
		aFloat := cast.ToFloat64(aVal)
		bFloat := cast.ToFloat64(bVal)
		// 对于浮点数比较，考虑精度问题
		return aFloat == bFloat
	}

	// 转换为字符串进行比较，不区分类型
	aStr := fmt.Sprintf("%v", a)
	bStr := fmt.Sprintf("%v", b)
	return aStr == bStr
}

func isNumeric(v reflect.Value) bool {
	switch v.Kind() {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64,
		reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64,
		reflect.Float32, reflect.Float64:
		return true
	default:
		return false
	}
}
